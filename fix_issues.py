#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复系统问题的脚本
"""

import sqlite3
import os
import sys

def fix_database_schema():
    """修复数据库表结构问题"""
    try:
        print("修复数据库表结构...")

        # 连接数据库
        conn = sqlite3.connect('heze_data.db')
        cursor = conn.cursor()

        # 检查system_config表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_config'")
        table_exists = cursor.fetchone()

        if not table_exists:
            print("创建system_config表...")
            cursor.execute('''
            CREATE TABLE system_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
        else:
            print("system_config表已存在")
            
        # 检查表结构
        cursor.execute("PRAGMA table_info(system_config)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"当前表结构: {column_names}")

        # 如果字段名不正确，需要重建表
        if 'config_key' in column_names or 'config_value' in column_names:
            print("重建system_config表...")
            
            # 备份现有数据
            cursor.execute("SELECT * FROM system_config")
            existing_data = cursor.fetchall()
            
            # 删除旧表
            cursor.execute("DROP TABLE system_config")
            
            # 创建新表
            cursor.execute('''
            CREATE TABLE system_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 恢复数据（如果有的话）
            for row in existing_data:
                if len(row) >= 2:
                    # 处理旧的字段名
                    key_val = row[0] if 'config_key' not in column_names else row[0]
                    value_val = row[1] if 'config_value' not in column_names else row[1]
                    
                    cursor.execute(
                        "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
                        (key_val, value_val)
                    )
        
        # 添加默认配置
        cursor.execute(
            "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
            ('server_host', '0.0.0.0')
        )
        cursor.execute(
            "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
            ('server_port', '5000')
        )
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库表结构修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复数据库表结构失败: {str(e)}")
        return False

def fix_network_settings():
    """修复网络设置"""
    try:
        print("🌐 检查网络设置...")
        
        # 检查防火墙设置
        print("🔥 建议的防火墙设置:")
        print("   netsh advfirewall firewall add rule name=\"Python Web App\" dir=in action=allow protocol=TCP localport=5000")
        
        # 检查网络连接
        import socket
        try:
            # 测试本地连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', 5000))
            sock.close()
            
            if result == 0:
                print("✅ 本地端口5000可访问")
            else:
                print("⚠️ 本地端口5000不可访问，可能应用未启动")
                
        except Exception as e:
            print(f"⚠️ 网络测试异常: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络设置检查失败: {str(e)}")
        return False

def fix_dingtalk_timeout():
    """修复钉钉超时问题"""
    try:
        print("📱 修复钉钉网络超时问题...")
        
        # 测试网络连接
        import requests
        
        test_urls = [
            "https://www.baidu.com",
            "https://api.dingtalk.com",
            "https://oapi.dingtalk.com"
        ]
        
        for url in test_urls:
            try:
                print(f"🔍 测试连接: {url}")
                response = requests.get(url, timeout=5)
                print(f"✅ {url} - 状态码: {response.status_code}")
            except requests.exceptions.Timeout:
                print(f"⏰ {url} - 连接超时")
            except requests.exceptions.ConnectionError:
                print(f"❌ {url} - 连接错误")
            except Exception as e:
                print(f"⚠️ {url} - 异常: {str(e)}")
        
        print("\n💡 钉钉网络问题解决建议:")
        print("1. 检查网络连接是否正常")
        print("2. 检查防火墙是否阻止了Python程序的网络访问")
        print("3. 检查代理设置")
        print("4. 尝试使用手机热点测试")
        print("5. 检查钉钉webhook地址是否正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 钉钉网络测试失败: {str(e)}")
        return False

def main():
    """主修复函数"""
    print("开始系统问题修复...")
    print("=" * 50)
    
    # 修复数据库问题
    db_fixed = fix_database_schema()
    
    # 修复网络设置
    network_fixed = fix_network_settings()
    
    # 修复钉钉超时问题
    dingtalk_fixed = fix_dingtalk_timeout()
    
    print("=" * 50)
    print("修复结果汇总:")
    print(f"   数据库修复: {'成功' if db_fixed else '失败'}")
    print(f"   网络设置: {'成功' if network_fixed else '失败'}")
    print(f"   钉钉网络: {'成功' if dingtalk_fixed else '失败'}")

    if all([db_fixed, network_fixed, dingtalk_fixed]):
        print("\n所有问题修复完成！请重启应用程序。")
        return 0
    else:
        print("\n部分问题未能完全修复，请查看上述详细信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
