#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复钉钉网络问题的脚本
"""

import requests
import time
import json

def test_dingtalk_webhook():
    """测试钉钉webhook连接"""
    try:
        print("测试钉钉webhook连接...")
        
        # 这里使用一个测试webhook（需要替换为实际的webhook）
        test_webhook = "https://oapi.dingtalk.com/robot/send?access_token=test"
        
        # 构造测试消息
        test_data = {
            "msgtype": "text",
            "text": {
                "content": "网络连接测试"
            }
        }
        
        # 设置不同的超时时间进行测试
        timeout_values = [5, 10, 15, 30]
        
        for timeout in timeout_values:
            try:
                print(f"测试超时时间: {timeout}秒")
                start_time = time.time()
                
                response = requests.post(
                    test_webhook, 
                    json=test_data, 
                    timeout=timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                print(f"  响应时间: {response_time:.2f}秒")
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"  响应内容: {result}")
                    
                    # 如果是无效token错误，说明网络连接正常
                    if result.get('errcode') == 310000:
                        print("  网络连接正常（无效token错误是预期的）")
                        return True
                    elif result.get('errcode') == 0:
                        print("  发送成功")
                        return True
                else:
                    print(f"  HTTP错误: {response.status_code}")
                
                break  # 成功连接就退出循环
                
            except requests.exceptions.Timeout:
                print(f"  超时 ({timeout}秒)")
                continue
            except requests.exceptions.ConnectionError as e:
                print(f"  连接错误: {str(e)}")
                continue
            except Exception as e:
                print(f"  其他错误: {str(e)}")
                continue
        
        return False
        
    except Exception as e:
        print(f"测试钉钉webhook失败: {str(e)}")
        return False

def test_image_upload():
    """测试图片上传服务"""
    try:
        print("测试图片上传服务...")
        
        # 测试不同的图床服务
        test_services = [
            ("ImgBB", "https://api.imgbb.com/1/upload"),
            ("SM.MS", "https://sm.ms/api/v2/upload"),
            ("Freeimage", "https://freeimage.host/api/1/upload")
        ]
        
        for name, url in test_services:
            try:
                print(f"测试 {name}: {url}")
                
                # 只测试连接，不实际上传
                response = requests.get(url.replace('/upload', ''), timeout=10)
                print(f"  连接状态: 可访问")
                
            except requests.exceptions.Timeout:
                print(f"  连接状态: 超时")
            except requests.exceptions.ConnectionError:
                print(f"  连接状态: 连接错误")
            except Exception as e:
                print(f"  连接状态: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"测试图片上传服务失败: {str(e)}")
        return False

def optimize_network_settings():
    """优化网络设置"""
    try:
        print("网络优化建议:")
        print("1. 增加超时时间到30秒")
        print("2. 添加重试机制")
        print("3. 使用连接池")
        print("4. 检查DNS设置")
        
        # 创建优化的requests会话
        session = requests.Session()
        
        # 设置适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        print("已创建优化的网络会话")
        
        return session
        
    except Exception as e:
        print(f"网络优化失败: {str(e)}")
        return None

def create_network_config():
    """创建网络配置文件"""
    try:
        print("创建网络配置文件...")
        
        config = {
            "timeout_settings": {
                "dingtalk_webhook": 30,
                "image_upload": 60,
                "general_request": 15
            },
            "retry_settings": {
                "max_retries": 3,
                "backoff_factor": 1,
                "retry_status_codes": [429, 500, 502, 503, 504]
            },
            "connection_pool": {
                "pool_connections": 10,
                "pool_maxsize": 20
            }
        }
        
        with open('network_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("网络配置文件已创建: network_config.json")
        return True
        
    except Exception as e:
        print(f"创建网络配置文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("钉钉网络问题诊断和修复")
    print("=" * 40)
    
    # 测试钉钉webhook
    webhook_ok = test_dingtalk_webhook()
    
    print("=" * 40)
    
    # 测试图片上传
    upload_ok = test_image_upload()
    
    print("=" * 40)
    
    # 优化网络设置
    session = optimize_network_settings()
    
    print("=" * 40)
    
    # 创建配置文件
    config_ok = create_network_config()
    
    print("=" * 40)
    print("诊断结果:")
    print(f"钉钉连接: {'正常' if webhook_ok else '异常'}")
    print(f"图片上传: {'正常' if upload_ok else '异常'}")
    print(f"网络优化: {'成功' if session else '失败'}")
    print(f"配置文件: {'成功' if config_ok else '失败'}")
    
    print("\n解决方案:")
    if not webhook_ok:
        print("1. 检查钉钉webhook地址是否正确")
        print("2. 检查网络防火墙设置")
        print("3. 尝试使用手机热点测试")
    
    if not upload_ok:
        print("4. 图片上传服务可能不稳定")
        print("5. 建议使用钉钉原生图片上传")
    
    print("6. 已创建优化的网络配置")
    print("7. 建议重启应用程序应用新配置")

if __name__ == "__main__":
    main()
