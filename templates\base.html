<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}菏泽数据提取工具{% endblock %}</title>
    <!-- 优先使用CDN，离线时自动降级到本地文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='{{ url_for('static', filename='css/bootstrap-local.css') }}';">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet"
          onerror="this.onerror=null;this.remove();">
    <!-- 离线备用样式 -->
    <noscript>
        <link href="{{ url_for('static', filename='css/bootstrap-local.css') }}" rel="stylesheet">
    </noscript>
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #e7f1ff;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f1ff;
        }
        .file-info {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        .table-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .correction-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 0;
            margin-top: 50px;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-file-earmark-excel"></i>
                菏泽数据提取工具
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link" href="{{ url_for('sector_data_manager') }}">
                    <i class="bi bi-database"></i> 扇区数据管理
                </a>
                <a class="nav-link" href="{{ url_for('trends_analysis') }}">
                    <i class="bi bi-graph-up"></i> 趋势分析
                </a>

                <a class="nav-link" href="{{ url_for('database_manager') }}">
                    <i class="bi bi-gear"></i> 数据库管理
                </a>
                <a class="nav-link" href="{{ url_for('history') }}">
                    <i class="bi bi-clock-history"></i> 历史记录
                </a>
                <a class="nav-link" href="#" onclick="goToEmailConfig()">
                    <i class="bi bi-envelope-gear"></i> 邮件配置
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'success' }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }}"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer">

            <div class="text-center text-muted">
                <small>&copy; 2024 菏泽数据提取工具 - 让数据处理更简单</small>
            </div>
        </div>
    </footer>

    <!-- 优先使用CDN，离线时自动降级到本地文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
            onerror="this.onerror=null;this.src='{{ url_for('static', filename='js/bootstrap-local.js') }}';">
    </script>
    <!-- 离线备用脚本 -->
    <script>
        // 检测Bootstrap是否加载成功，如果失败则加载本地版本
        if (typeof bootstrap === 'undefined') {
            var script = document.createElement('script');
            script.src = '{{ url_for("static", filename="js/bootstrap-local.js") }}';
            document.head.appendChild(script);
        }
    </script>

    <script>
        // 智能导航功能
        function goToEmailConfig() {
            const currentUrl = window.location.href;
            const urlParams = new URLSearchParams(window.location.search);

            // 构建返回URL
            let configUrl = '/email_config';

            // 如果当前在结果页面，传递相关参数
            if (currentUrl.includes('/results')) {
                const date = urlParams.get('date');
                const fromHistory = urlParams.get('from_history');

                if (date) {
                    configUrl += `?from_results=true&date=${date}`;
                } else {
                    configUrl += `?from_results=true`;
                }
            } else if (!currentUrl.includes('/email_config') && !currentUrl.includes('/upload')) {
                // 如果不是配置页面且不是upload页面，传递当前URL作为返回地址
                configUrl += `?return_url=${encodeURIComponent(currentUrl)}`;
            }

            window.location.href = configUrl;
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
