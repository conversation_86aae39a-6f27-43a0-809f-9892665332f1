#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认设置的脚本
"""

import re

def test_combine_all_charts_default():
    """测试三合一选项是否默认勾选"""
    try:
        print("测试三合一选项默认设置...")
        
        # 读取results.html文件
        with open('templates/results.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找三合一选项的复选框
        pattern = r'<input[^>]*id="combineAllCharts"[^>]*>'
        match = re.search(pattern, content)
        
        if match:
            checkbox_html = match.group(0)
            print(f"找到三合一选项: {checkbox_html}")
            
            # 检查是否包含checked属性
            if 'checked' in checkbox_html:
                print("✅ 三合一选项已设置为默认勾选")
                return True
            else:
                print("❌ 三合一选项未设置为默认勾选")
                return False
        else:
            print("❌ 未找到三合一选项")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def test_other_default_settings():
    """测试其他默认设置"""
    try:
        print("\n测试其他默认设置...")
        
        with open('templates/results.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 测试项目列表
        test_items = [
            ('includeTrends', '包含趋势图'),
            ('includeCharts', '同时发送趋势图'),
            ('includeSummary', '包含文字摘要'),
            ('includeTrendCharts', '在邮件中嵌入趋势图')
        ]
        
        results = {}
        
        for item_id, description in test_items:
            pattern = rf'<input[^>]*id="{item_id}"[^>]*>'
            match = re.search(pattern, content)
            
            if match:
                checkbox_html = match.group(0)
                is_checked = 'checked' in checkbox_html
                results[item_id] = {
                    'description': description,
                    'checked': is_checked,
                    'html': checkbox_html
                }
                status = "✅ 已勾选" if is_checked else "⭕ 未勾选"
                print(f"  {description}: {status}")
            else:
                print(f"  {description}: ❌ 未找到")
                results[item_id] = {
                    'description': description,
                    'checked': False,
                    'html': None
                }
        
        return results
        
    except Exception as e:
        print(f"测试其他默认设置失败: {str(e)}")
        return {}

def test_select_default_values():
    """测试下拉选择框的默认值"""
    try:
        print("\n测试下拉选择框默认值...")
        
        with open('templates/results.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 测试下拉框默认值
        select_tests = [
            ('globalApiType', '官方API'),
            ('dingtalkTrendTimeRange', '当月数据'),
            ('reportFormat', '报告格式'),
            ('imageUploadMethod', '图片上传方式')
        ]
        
        for select_id, description in select_tests:
            # 查找select元素
            select_pattern = rf'<select[^>]*id="{select_id}"[^>]*>.*?</select>'
            select_match = re.search(select_pattern, content, re.DOTALL)
            
            if select_match:
                select_html = select_match.group(0)
                
                # 查找selected选项
                option_pattern = r'<option[^>]*selected[^>]*>(.*?)</option>'
                option_match = re.search(option_pattern, select_html)
                
                if option_match:
                    selected_text = option_match.group(1).strip()
                    print(f"  {description}: ✅ 默认选择 '{selected_text}'")
                else:
                    print(f"  {description}: ⭕ 无默认选择")
            else:
                print(f"  {description}: ❌ 未找到")
        
        return True
        
    except Exception as e:
        print(f"测试下拉选择框默认值失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("默认设置测试")
    print("=" * 40)
    
    # 测试三合一选项
    combine_ok = test_combine_all_charts_default()
    
    # 测试其他默认设置
    other_settings = test_other_default_settings()
    
    # 测试下拉选择框
    select_ok = test_select_default_values()
    
    print("\n" + "=" * 40)
    print("测试结果汇总:")
    print(f"三合一选项: {'✅ 默认勾选' if combine_ok else '❌ 未勾选'}")
    
    if other_settings:
        checked_count = sum(1 for item in other_settings.values() if item['checked'])
        total_count = len(other_settings)
        print(f"其他选项: {checked_count}/{total_count} 项默认勾选")
    
    print(f"下拉选择框: {'✅ 测试完成' if select_ok else '❌ 测试失败'}")
    
    if combine_ok:
        print("\n🎉 三合一选项已成功设置为默认勾选！")
        print("现在用户打开页面时，三合一选项将自动勾选。")
    else:
        print("\n⚠️ 三合一选项设置可能有问题，请检查。")

if __name__ == "__main__":
    main()
