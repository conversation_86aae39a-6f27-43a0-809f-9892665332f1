#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版测试脚本
"""

import re

def test_combine_all_charts():
    """测试三合一选项"""
    try:
        print("测试三合一选项默认设置...")
        
        with open('templates/results.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找三合一选项
        pattern = r'<input[^>]*id="combineAllCharts"[^>]*>'
        match = re.search(pattern, content)
        
        if match:
            checkbox_html = match.group(0)
            print(f"找到选项: {checkbox_html}")
            
            if 'checked' in checkbox_html:
                print("成功: 三合一选项已设置为默认勾选")
                return True
            else:
                print("失败: 三合一选项未勾选")
                return False
        else:
            print("错误: 未找到三合一选项")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("默认设置测试")
    print("=" * 30)
    
    result = test_combine_all_charts()
    
    print("=" * 30)
    if result:
        print("测试通过: 三合一选项默认勾选")
        print("用户现在打开页面时会自动勾选三合一选项")
    else:
        print("测试失败: 需要检查设置")

if __name__ == "__main__":
    main()
