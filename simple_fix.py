#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版系统修复脚本
"""

import sqlite3
import os

def fix_database():
    """修复数据库问题"""
    try:
        print("修复数据库表结构...")
        
        conn = sqlite3.connect('heze_data.db')
        cursor = conn.cursor()
        
        # 检查system_config表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_config'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("创建system_config表...")
            cursor.execute('''
            CREATE TABLE system_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(system_config)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"当前表结构: {column_names}")
        
        # 如果字段名不正确，重建表
        if 'config_key' in column_names or 'config_value' in column_names:
            print("重建system_config表...")
            
            # 备份数据
            cursor.execute("SELECT * FROM system_config")
            existing_data = cursor.fetchall()
            
            # 删除旧表
            cursor.execute("DROP TABLE system_config")
            
            # 创建新表
            cursor.execute('''
            CREATE TABLE system_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 恢复数据
            for row in existing_data:
                if len(row) >= 2:
                    cursor.execute(
                        "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
                        (row[0], row[1])
                    )
        
        # 添加默认配置
        cursor.execute(
            "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
            ('server_host', '0.0.0.0')
        )
        cursor.execute(
            "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
            ('server_port', '5000')
        )
        
        conn.commit()
        conn.close()
        
        print("数据库修复完成")
        return True
        
    except Exception as e:
        print(f"数据库修复失败: {str(e)}")
        return False

def test_network():
    """测试网络连接"""
    try:
        print("测试网络连接...")
        
        import requests
        
        test_urls = [
            ("百度", "https://www.baidu.com"),
            ("钉钉API", "https://api.dingtalk.com"),
            ("钉钉OAPI", "https://oapi.dingtalk.com")
        ]
        
        for name, url in test_urls:
            try:
                print(f"测试 {name}: {url}")
                response = requests.get(url, timeout=5)
                print(f"  状态码: {response.status_code}")
            except requests.exceptions.Timeout:
                print(f"  连接超时")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误")
            except Exception as e:
                print(f"  异常: {str(e)}")
        
        print("\n网络问题解决建议:")
        print("1. 检查网络连接")
        print("2. 检查防火墙设置")
        print("3. 检查代理设置")
        print("4. 检查钉钉webhook地址")
        
        return True
        
    except Exception as e:
        print(f"网络测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始系统修复...")
    print("=" * 40)
    
    # 修复数据库
    db_ok = fix_database()
    
    print("=" * 40)
    
    # 测试网络
    net_ok = test_network()
    
    print("=" * 40)
    print("修复结果:")
    print(f"数据库: {'成功' if db_ok else '失败'}")
    print(f"网络测试: {'成功' if net_ok else '失败'}")
    
    if db_ok:
        print("\n数据库问题已修复，请重启应用程序")
    else:
        print("\n数据库修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
