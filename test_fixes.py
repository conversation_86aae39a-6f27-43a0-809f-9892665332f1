#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import sqlite3
import requests
import time

def test_database_fix():
    """测试数据库修复效果"""
    try:
        print("测试数据库修复效果...")
        
        conn = sqlite3.connect('heze_data.db')
        cursor = conn.cursor()
        
        # 测试获取服务器配置
        cursor.execute("SELECT key, value FROM system_config WHERE key IN ('server_host', 'server_port')")
        configs = cursor.fetchall()
        
        config_dict = {}
        for key, value in configs:
            if key == 'server_host':
                config_dict['host'] = value
            elif key == 'server_port':
                config_dict['port'] = int(value)
        
        conn.close()
        
        print(f"服务器配置: {config_dict}")
        
        if 'host' in config_dict and 'port' in config_dict:
            print("数据库修复成功")
            return True
        else:
            print("数据库修复失败")
            return False
            
    except Exception as e:
        print(f"数据库测试失败: {str(e)}")
        return False

def test_network_timeout():
    """测试网络超时设置"""
    try:
        print("测试网络超时设置...")
        
        # 测试不同的超时时间
        test_url = "https://httpbin.org/delay/5"  # 延迟5秒的测试URL
        
        timeout_tests = [
            (3, "应该超时"),
            (10, "应该成功"),
            (30, "应该成功")
        ]
        
        for timeout, expected in timeout_tests:
            try:
                print(f"测试超时时间 {timeout}秒 ({expected})")
                start_time = time.time()
                
                response = requests.get(test_url, timeout=timeout)
                
                end_time = time.time()
                actual_time = end_time - start_time
                
                print(f"  实际响应时间: {actual_time:.2f}秒")
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("  请求成功")
                
            except requests.exceptions.Timeout:
                print(f"  请求超时 (预期: {expected})")
            except Exception as e:
                print(f"  请求异常: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"网络超时测试失败: {str(e)}")
        return False

def test_dingtalk_connection():
    """测试钉钉连接"""
    try:
        print("测试钉钉连接...")
        
        # 测试钉钉API域名解析
        dingtalk_domains = [
            "api.dingtalk.com",
            "oapi.dingtalk.com"
        ]
        
        for domain in dingtalk_domains:
            try:
                print(f"测试域名: {domain}")
                
                # 简单的连接测试
                response = requests.get(f"https://{domain}", timeout=10)
                print(f"  连接成功，状态码: {response.status_code}")
                
            except requests.exceptions.Timeout:
                print(f"  连接超时")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误")
            except Exception as e:
                print(f"  其他错误: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"钉钉连接测试失败: {str(e)}")
        return False

def create_test_report():
    """创建测试报告"""
    try:
        print("创建测试报告...")
        
        report = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "database_fix": test_database_fix(),
            "network_timeout": test_network_timeout(),
            "dingtalk_connection": test_dingtalk_connection()
        }
        
        print("\n" + "="*50)
        print("测试报告")
        print("="*50)
        print(f"测试时间: {report['test_time']}")
        print(f"数据库修复: {'通过' if report['database_fix'] else '失败'}")
        print(f"网络超时: {'通过' if report['network_timeout'] else '失败'}")
        print(f"钉钉连接: {'通过' if report['dingtalk_connection'] else '失败'}")
        
        # 计算总体结果
        total_tests = 3
        passed_tests = sum([
            report['database_fix'],
            report['network_timeout'],
            report['dingtalk_connection']
        ])
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过")
        
        if passed_tests == total_tests:
            print("所有测试通过，系统修复成功！")
            print("\n建议:")
            print("1. 重启应用程序")
            print("2. 测试钉钉推送功能")
            print("3. 如果仍有问题，检查钉钉webhook配置")
        else:
            print("部分测试失败，需要进一步排查")
            
            if not report['database_fix']:
                print("- 数据库问题未解决")
            if not report['network_timeout']:
                print("- 网络超时问题未解决")
            if not report['dingtalk_connection']:
                print("- 钉钉连接问题未解决")
        
        return report
        
    except Exception as e:
        print(f"创建测试报告失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("系统修复效果测试")
    print("="*50)
    
    # 运行测试并生成报告
    report = create_test_report()
    
    if report:
        print("\n测试完成")
    else:
        print("\n测试失败")

if __name__ == "__main__":
    main()
